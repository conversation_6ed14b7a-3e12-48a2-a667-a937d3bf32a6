import { getCurrentUserRole, getEndpointsByRole, STATISTICS_ENDPOINTS } from './endpoints';

// Helper function để lấy token từ localStorage
const getAuthToken = () => {
  try {
    const tokenFromStorage = localStorage.getItem('token');
    const authToken = localStorage.getItem('authToken');
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    const userToken = user.token;
    return tokenFromStorage || authToken || userToken;
  } catch (error) {
    console.error('Error getting auth token:', error);
    return null;
  } 
};

// Helper function để tạo headers với authorization
const getAuthHeaders = () => {
  const token = getAuthToken();
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bearer ${token}` })
  };
};

// Helper function để xử lý response
const handleResponse = async (response) => {
  if (!response.ok) {
    let error = { message: 'Có lỗi xảy ra' };
    try {
      error = await response.json();
    } catch {}
    throw new Error(error.message || 'Có lỗi xảy ra');
  }
  return response.json();
};

// Helper function để lấy endpoints dựa trên role hiện tại
const getCurrentEndpoints = () => {
  const role = getCurrentUserRole();
  return getEndpointsByRole(role);
};

// ========== STATISTICS MANAGEMENT FUNCTIONS ==========

// Lấy thống kê tỉ lệ hoàn thành task theo phòng ban theo tháng
export async function getDepartmentTaskCompletionRate(year, departmentId) {
  try {
    if (!year || !departmentId) {
      throw new Error('Thiếu thông tin năm hoặc phòng ban');
    }

    const url = STATISTICS_ENDPOINTS.DEPARTMENT_TASK_COMPLETION_RATE(year, departmentId);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy thống kê phòng ban
export async function getDepartmentStats(params = {}) {
  try {
    const queryString = new URLSearchParams(params).toString();
    const url = `${STATISTICS_ENDPOINTS.DEPARTMENT_STATS}${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy thống kê người dùng
export async function getUserStats(params = {}) {
  try {
    const queryString = new URLSearchParams(params).toString();
    const url = `${STATISTICS_ENDPOINTS.USER_STATS}${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy thống kê dự án theo phòng ban
export async function getProjectsByDepartment(params = {}) {
  try {
    const queryString = new URLSearchParams(params).toString();
    const url = `${STATISTICS_ENDPOINTS.PROJECTS_BY_DEPARTMENT}${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy thống kê tất cả dự án của các phòng ban
export async function getAllDepartmentsProjects(params = {}) {
  try {
    const queryString = new URLSearchParams(params).toString();
    const url = `${STATISTICS_ENDPOINTS.ALL_DEPARTMENTS_PROJECTS}${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy thống kê phân công task cho nhân viên
export async function getStaffTaskAssignments(params = {}) {
  try {
    const queryString = new URLSearchParams(params).toString();
    const url = `${STATISTICS_ENDPOINTS.STAFF_TASK_ASSIGNMENTS}${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Lấy dashboard cho staff
export async function getStaffDashboard(params = {}) {
  try {
    const queryString = new URLSearchParams(params).toString();
    const url = `${STATISTICS_ENDPOINTS.STAFF_DASHBOARD}${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders(),
    });
    return handleResponse(response);
  } catch (err) {
    if (err instanceof TypeError && err.message === 'Failed to fetch') {
      throw new Error('Không thể kết nối tới máy chủ.');
    }
    throw err;
  }
}

// Transform dữ liệu thống kê từ backend về format frontend
export const transformStatisticsData = (backendStats) => {
  return {
    id: backendStats._id || backendStats.id || 'N/A',
    departmentId: backendStats.departmentId || 'N/A',
    departmentName: backendStats.departmentName || 'Chưa có tên',
    year: backendStats.year || new Date().getFullYear(),
    month: backendStats.month || 1,
    totalTasks: backendStats.totalTasks || 0,
    completedTasks: backendStats.completedTasks || 0,
    completionRate: backendStats.completionRate || 0,
    createdAt: backendStats.createdAt ? new Date(backendStats.createdAt).toLocaleDateString('vi-VN') : 'N/A',
    updatedAt: backendStats.updatedAt ? new Date(backendStats.updatedAt).toLocaleDateString('vi-VN') : 'N/A'
  };
};

// Kiểm tra quyền truy cập thống kê theo backend middleware
export const checkStatisticsPermissions = () => {
  const role = getCurrentUserRole();
  return {
    canViewStatistics: true, // Tất cả role đều có thể xem thống kê cơ bản
    canViewDepartmentStats: ['admin', 'ceo', 'hr', 'leader', 'departmenthead'].includes(role),
    canViewAllStats: ['admin', 'ceo', 'hr'].includes(role),
    canExportStats: ['admin', 'ceo', 'hr', 'leader'].includes(role),
    role: role
  };
};

// Export getCurrentUserRole để sử dụng ở nơi khác
export { getCurrentUserRole };
