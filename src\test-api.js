// Test file to debug API integration
import { getDepartmentTaskCompletionRate } from './api/statisticsManagement';

// Test function
export const testDepartmentTaskCompletionAPI = async () => {
  console.log('=== Testing Department Task Completion API ===');
  
  try {
    // Test with the exact parameters from your Postman test
    const year = '2025';
    const departmentId = '687a57544e3a480b780b146e';
    
    console.log(`Testing with year: ${year}, departmentId: ${departmentId}`);
    
    const result = await getDepartmentTaskCompletionRate(year, departmentId);
    
    console.log('API Response:', result);
    
    if (result.success && result.data && result.data.monthlyData) {
      console.log('Monthly Data:', result.data.monthlyData);
      
      // Extract completion rates
      const completionRates = result.data.monthlyData.map(month => ({
        month: month.month,
        monthName: month.monthName,
        completionRate: month.stats.completionRate
      }));
      
      console.log('Completion Rates by Month:', completionRates);
      
      // Create chart data points
      const dataPoints = Array.from({ length: 12 }, (_, index) => {
        const month = index + 1;
        const monthData = result.data.monthlyData.find(item => item.month === month);
        return monthData && monthData.stats ? Math.round(monthData.stats.completionRate) : 0;
      });
      
      console.log('Chart Data Points:', dataPoints);
      
      return {
        success: true,
        dataPoints,
        monthlyData: result.data.monthlyData
      };
    } else {
      console.error('Invalid API response structure:', result);
      return { success: false, error: 'Invalid response structure' };
    }
    
  } catch (error) {
    console.error('API Test Error:', error);
    return { success: false, error: error.message };
  }
};

// Sample data test
export const testWithSampleData = () => {
  console.log('=== Testing with Sample Data ===');
  
  const sampleData = {
    "success": true,
    "data": {
      "monthlyData": [
        { "month": 1, "stats": { "completionRate": 0 } },
        { "month": 2, "stats": { "completionRate": 100 } },
        { "month": 3, "stats": { "completionRate": 0 } },
        { "month": 4, "stats": { "completionRate": 0 } },
        { "month": 5, "stats": { "completionRate": 0 } },
        { "month": 6, "stats": { "completionRate": 0 } },
        { "month": 7, "stats": { "completionRate": 0 } },
        { "month": 8, "stats": { "completionRate": 58.33 } },
        { "month": 9, "stats": { "completionRate": 0 } },
        { "month": 10, "stats": { "completionRate": 0 } },
        { "month": 11, "stats": { "completionRate": 0 } },
        { "month": 12, "stats": { "completionRate": 0 } }
      ]
    }
  };
  
  if (sampleData.success && sampleData.data && sampleData.data.monthlyData) {
    const monthlyData = sampleData.data.monthlyData;
    
    console.log('Sample Monthly Data:', monthlyData);
    
    // Create chart data points
    const dataPoints = Array.from({ length: 12 }, (_, index) => {
      const month = index + 1;
      const monthData = monthlyData.find(item => item.month === month);
      const completionRate = monthData && monthData.stats ? Math.round(monthData.stats.completionRate) : 0;
      console.log(`Month ${month}: ${completionRate}%`);
      return completionRate;
    });
    
    console.log('Sample Chart Data Points:', dataPoints);
    
    // Expected result: [0, 100, 0, 0, 0, 0, 0, 58, 0, 0, 0, 0]
    
    return {
      success: true,
      dataPoints,
      monthlyData
    };
  }
  
  return { success: false, error: 'Invalid sample data' };
};

// Run tests
if (typeof window !== 'undefined') {
  // Browser environment
  window.testDepartmentTaskCompletionAPI = testDepartmentTaskCompletionAPI;
  window.testWithSampleData = testWithSampleData;
  
  console.log('Test functions available:');
  console.log('- window.testDepartmentTaskCompletionAPI()');
  console.log('- window.testWithSampleData()');
}
